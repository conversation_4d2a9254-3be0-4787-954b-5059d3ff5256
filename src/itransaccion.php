<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/transaccion.php';
require_once __ROOT__ . '/src/classes/categoriatransaccion.php';
require_once __ROOT__ . '/src/classes/transaccioncategoria.php';
require_once __ROOT__ . '/src/classes/budget.php';
require_once __ROOT__ . '/src/general/preparar.php';

$newtransaccion = new Transaccion();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		$newtransaccion->fecha = create_date();
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get
#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
	try {
		$conexion->beginTransaction();
		
		$newtransaccion->tipo              = limpiar_datos($_POST['tipo_transaccion']);
		$newtransaccion->valor             = limpiar_datos($_POST['valor']);
		$newtransaccion->fecha             = limpiar_datos($_POST['fecha']);
		$newtransaccion->nota              = limpiar_datos($_POST['nota']);
		$newtransaccion->budget            = new Budget();
		$newtransaccion->budget->id_budget = limpiar_datos($_POST['id_budget']);

		$id_categoriatransaccion = limpiar_datos($_POST['id_categoriatransaccion']);
		if (!empty($id_categoriatransaccion)) {
			$newtransaccion->getListCategorias(array($id_categoriatransaccion));
		} else {
			$newtransaccion->getListCategorias(array()); // Pass empty array if no category is selected
		}
		$newtransaccion->add($conexion);
		
		$conexion->commit();
		
		header('Location: gtransacciones?i=1');
		
	} catch (Exception $e) {
		$conexion->rollback();
		
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_add
#region try
try {
	$categs = CategoriaTransaccion::getList($conexion);
	
	$param               = array();
	$param['includetrk'] = 0;
	$budgets             = Budget::getList($param, $conexion);
	
	$unique_notas = Transaccion::getUniqueNotasLastYear($conexion);
	
} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try


require_once __ROOT__ . '/views/itransaccion.view.php';

?>
