<?php
#region region DOCS
/** @var CategoriaTransaccion[] $categs */
/** @var Transaccion $newtransaccion */
/** @var Budget[] $budgets */
/** @var string[] $unique_notas */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Transacciones</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <link href="<?php echo RUTA ?>resources/assets/plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
    <link href="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
	<?php #region region CSS grouped controls ?>
	<link href="<?php echo RUTA ?>resources/css/grouped_controls.css" rel="stylesheet" />
	<?php #endregion CSS grouped controls ?>
	<?php #region region CSS select choices.js  ?>
	<link rel="stylesheet" href="<?php echo RUTA ?>resources/choices.js/choices.min.css">
	<link rel="stylesheet" href="<?php echo RUTA ?>resources/choices.js/fab_choices.css">
	<?php #endregion CSS select choices.js  ?>
    <?php #endregion head ?>

</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <!-- BEGIN page-header -->
        <h1 class="page-header">Transacciones</h1>
        <!-- END page-header -->

        <?php #region region FORM ?>
        <form action="itransaccion" method="POST">
            <!-- BEGIN row -->
            <div class="row mt-3">
	            <div class="container">
		            <div class="btn-group btn-group-toggle" data-toggle="buttons">
			            <?php foreach ($budgets as $budget): ?>
			            <label class="btn btn-secondary">
				            <input type="radio" name="id_budget" autocomplete="off" value="<?php echo $budget->id_budget; ?>"> <?php echo $budget->canal; ?>
			            </label>
			            <?php endforeach; ?>
		            </div>
	            </div>
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
	            <div class="container">
		            <div class="btn-group btn-group-toggle" data-toggle="buttons">
			            <label class="btn btn-secondary">
				            <input type="radio" name="tipo_transaccion" autocomplete="off" value="INGRESO"> Ingreso
			            </label>
			            <label class="btn btn-secondary">
				            <input type="radio" name="tipo_transaccion" autocomplete="off" value="EGRESO"> Egreso
			            </label>
		            </div>
	            </div>
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
	            <!-- BEGIN date fecha -->
	            <div class="col-md-3 col-xs-12">
		            <div class="mb-3">
			            <label for="fecha" class="form-label">Fecha:</label>
			            <div class="input-group">
				            <input type="text" id="fecha" name="fecha" value="<?php echo @recover_var($newtransaccion->fecha) ?>" class="form-control datepicker" autocomplete="off"/>
				            <span class="input-group-text">
                            <i class="fa fa-calendar-alt"></i>
                        </span>
			            </div>
		            </div>
	            </div>
	            <!-- END date -->

                <!-- BEGIN select -->
	            <div class="col-md-3 col-xs-12">
		            <div class="mb-3">
			            <label for="categorias" class="form-label">Categorias:</label>
			            <select id="categorias" name="id_categoriatransaccion" class="form-select">
				            <option value="">-- Seleccione Categoria --</option>
				            <?php foreach ($categs as $categ): ?>
					            <option value="<?php echo limpiar_datos($categ->id); ?>">
						            <?php echo $categ->nombre; ?>
					            </option>
				            <?php endforeach; ?>
			            </select>
		            </div>
	            </div>
                <!-- END select -->

	            <!-- BEGIN text valor -->
	            <div class="col-md-3 col-xs-12">
		            <div class="mb-3">
			            <label for="valor" class="form-label">Valor:</label>
			            <input type="text" name="valor" id="valor" value="<?php echo @recover_var($newtransaccion->valor) ?>" class="form-control" onclick="this.focus();this.select('')"/>
		            </div>
	            </div>
	            <!-- END text valor -->

                <!-- BEGIN text -->
	            <div class="col-md-3 col-xs-12">
		            <div class="mb-3">
			            <label for="nota" class="form-label">Nota:</label>
			            <input type="text" name="nota" id="nota" value="<?php echo @recover_var($newtransaccion->nota) ?>" class="form-control"/>
		            </div>
	            </div>
                <!-- END text -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
	        <div class="row mt-3">
		        <?php #region region SUBMIT sub_add ?>
		        <div class="col-md-12 col-xs-12">
			        <button type="submit" id="sub_add" name="sub_add" class="btn btn-success w-100">
				        Agregar
			        </button>
		        </div>
		        <?php #endregion sub_add ?>
	        </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN link -->
                <div class="col-md-12 col-xs-12">
                    <a href="gtransacciones" class="btn btn-default w-100">
                        Regresar
                    </a>
                </div>
                <!-- END link -->
            </div>
            <!-- END row -->
        </form>
        <?php #endregion form ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<?php #region region JS date ?>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/js/datepickerini.js"></script>
<?php #endregion datejs ?>
<?php #region region JS grouped controls ?>
<script src="<?php echo RUTA ?>resources/js/grouped_controls.js"></script>
<?php #endregion JS grouped controls ?>
<?php #region region JS select choices.js ?>
<script src="<?php echo RUTA ?>resources/choices.js/choices.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const categoriasSelect = document.getElementById('categorias');
        if (categoriasSelect) {
            new Choices(categoriasSelect, {
                searchEnabled: true,
                shouldSort: false,
                placeholder: true 
            });
        }
    });
</script>
<script type="text/javascript">
    $(function() {
        const availableNotes = <?php echo json_encode($unique_notas ?? []); ?>; // Ensure $unique_notas is defined, default to empty array
        $("#nota").autocomplete({
            source: availableNotes,
            minLength: 3 // Start searching after 3 character
        });
    });
</script>
<?php #endregion JS select choices.js ?>
<?php #endregion js ?>

</body>
</html>